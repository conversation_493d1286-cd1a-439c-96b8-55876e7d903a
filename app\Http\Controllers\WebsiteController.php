<?php

namespace App\Http\Controllers;

use App\Models\Home;
use App\Models\PrivacyAndTerm;
use Illuminate\Http\Request;

class WebsiteController extends Controller
{
    public function index() {
        $page = Home::with('details')->first();
        return view('website.index',compact('page'));
    }
    public function services() {
        return view('website.service');
    }
    public function professional() {
        return view('website.professional');
    }
    public function privacyPolicy() {
        $policies = PrivacyAndTerm::where('type','privacy')->get();
        return view('website.privacy-policy',compact('policies'));
    }
    public function terms() {
        $terms = PrivacyAndTerm::where('type','term')->get();
        return view('website.term',compact('terms'));
    }
}
