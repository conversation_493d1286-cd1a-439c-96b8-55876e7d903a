<?php $__env->startPush('css'); ?>
    <style>

    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="privacy-policy padding-block">
        <div class="container">
            <div class="row">
                <div class="col-md-4 ">
                    <div class="position-sticky top-0 d-flex flex-column gap-3">
                        <?php $__currentLoopData = $policies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $policy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="#section-<?php echo e($index); ?>" class="nav-link table-content link fs-16"><?php echo e($policy->title); ?></a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <div class="col-md-8">
                    <h6 class="black mb-5">Stylenest Privacy Policy</h6>
                    <div class="terms-condition">
                        <?php $__currentLoopData = $policies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $policy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div id="section-<?php echo e($index); ?>">
                                <h6><?php echo e($policy->title); ?></h6>
                                <p>
                                    <?php echo $policy->description; ?>

                                </p>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/website/privacy-policy.blade.php ENDPATH**/ ?>